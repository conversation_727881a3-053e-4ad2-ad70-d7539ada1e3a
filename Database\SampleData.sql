-- =====================================================
-- MyHRM - Sample Data Script
-- Oracle 11g Sample Data for Testing
-- =====================================================

-- Enable DBMS_OUTPUT for feedback
SET SERVEROUTPUT ON;

BEGIN
    DBMS_OUTPUT.PUT_LINE('=== Loading Sample Data for MyHRM ===');
    DBMS_OUTPUT.PUT_LINE('Date: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'));
END;
/

-- =====================================================
-- 1. INSERT SAMPLE EMPLOYEES USING PACKAGE PROCEDURE
-- =====================================================

DECLARE
    v_employee_id NUMBER;
BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Inserting sample employees...');
    
    -- Employee 1: John Smith
    emp_pkg.add_employee(
        p_name => 'John Smith',
        p_hire_date => DATE '2020-01-15',
        p_salary => 75000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added John Smith (ID: ' || v_employee_id || ')');
    
    -- Employee 2: Sarah Johnson
    emp_pkg.add_employee(
        p_name => 'Sarah Johnson',
        p_hire_date => DATE '2019-03-22',
        p_salary => 82000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Sarah Johnson (ID: ' || v_employee_id || ')');
    
    -- Employee 3: Michael Brown
    emp_pkg.add_employee(
        p_name => 'Michael Brown',
        p_hire_date => DATE '2021-07-10',
        p_salary => 65000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Michael Brown (ID: ' || v_employee_id || ')');
    
    -- Employee 4: Emily Davis
    emp_pkg.add_employee(
        p_name => 'Emily Davis',
        p_hire_date => DATE '2018-11-05',
        p_salary => 95000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Emily Davis (ID: ' || v_employee_id || ')');
    
    -- Employee 5: David Wilson
    emp_pkg.add_employee(
        p_name => 'David Wilson',
        p_hire_date => DATE '2022-02-28',
        p_salary => 58000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added David Wilson (ID: ' || v_employee_id || ')');
    
    -- Employee 6: Lisa Anderson
    emp_pkg.add_employee(
        p_name => 'Lisa Anderson',
        p_hire_date => DATE '2020-09-14',
        p_salary => 71000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Lisa Anderson (ID: ' || v_employee_id || ')');
    
    -- Employee 7: Robert Taylor
    emp_pkg.add_employee(
        p_name => 'Robert Taylor',
        p_hire_date => DATE '2017-05-30',
        p_salary => 88000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Robert Taylor (ID: ' || v_employee_id || ')');
    
    -- Employee 8: Jennifer Martinez
    emp_pkg.add_employee(
        p_name => 'Jennifer Martinez',
        p_hire_date => DATE '2021-12-01',
        p_salary => 62000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Jennifer Martinez (ID: ' || v_employee_id || ')');
    
    -- Employee 9: Christopher Lee
    emp_pkg.add_employee(
        p_name => 'Christopher Lee',
        p_hire_date => DATE '2019-08-18',
        p_salary => 79000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Christopher Lee (ID: ' || v_employee_id || ')');
    
    -- Employee 10: Amanda White
    emp_pkg.add_employee(
        p_name => 'Amanda White',
        p_hire_date => DATE '2023-01-09',
        p_salary => 55000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Amanda White (ID: ' || v_employee_id || ')');
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error inserting sample data: ' || SQLERRM);
        ROLLBACK;
        RAISE;
END;
/

-- =====================================================
-- 2. DEMONSTRATE SALARY UPDATES (TRIGGERS)
-- =====================================================

DECLARE
    v_employee_id NUMBER;
    v_old_salary NUMBER;
    v_new_salary NUMBER;
BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Demonstrating salary updates...');
    
    -- Get first employee for salary update demonstration
    SELECT id, salary INTO v_employee_id, v_old_salary 
    FROM employees 
    WHERE ROWNUM = 1;
    
    v_new_salary := v_old_salary + 5000;
    
    -- Update salary using package procedure (will trigger audit log)
    emp_pkg.update_employee_salary(v_employee_id, v_new_salary);
    
    DBMS_OUTPUT.PUT_LINE('✓ Updated employee ' || v_employee_id || 
                        ' salary from $' || v_old_salary || ' to $' || v_new_salary);
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error updating salary: ' || SQLERRM);
END;
/

-- =====================================================
-- 3. DISPLAY SAMPLE DATA SUMMARY
-- =====================================================

DECLARE
    v_total_employees NUMBER;
    v_avg_salary NUMBER(10,2);
    v_min_salary NUMBER(10,2);
    v_max_salary NUMBER(10,2);
    v_total_logs NUMBER;
BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== SAMPLE DATA SUMMARY ===');
    
    -- Get employee statistics
    SELECT COUNT(*), AVG(salary), MIN(salary), MAX(salary)
    INTO v_total_employees, v_avg_salary, v_min_salary, v_max_salary
    FROM employees;
    
    -- Get log count
    SELECT COUNT(*) INTO v_total_logs FROM employee_log;
    
    DBMS_OUTPUT.PUT_LINE('Total Employees: ' || v_total_employees);
    DBMS_OUTPUT.PUT_LINE('Average Salary: $' || TO_CHAR(v_avg_salary, '999,999.99'));
    DBMS_OUTPUT.PUT_LINE('Salary Range: $' || TO_CHAR(v_min_salary, '999,999.99') || 
                        ' - $' || TO_CHAR(v_max_salary, '999,999.99'));
    DBMS_OUTPUT.PUT_LINE('Audit Log Entries: ' || v_total_logs);
    
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Employee List:');
    
    FOR emp_rec IN (SELECT id, name, salary, hire_date FROM employees ORDER BY id) LOOP
        DBMS_OUTPUT.PUT_LINE('  ' || emp_rec.id || '. ' || emp_rec.name || 
                           ' - $' || TO_CHAR(emp_rec.salary, '999,999.99') ||
                           ' (Hired: ' || TO_CHAR(emp_rec.hire_date, 'YYYY-MM-DD') || ')');
    END LOOP;
    
END;
/

-- =====================================================
-- 4. TEST PL/SQL FUNCTIONS
-- =====================================================

DECLARE
    v_employee_id NUMBER := 1;
    v_salary NUMBER(10,2);
    v_bonus NUMBER(10,2);
BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== TESTING PL/SQL FUNCTIONS ===');
    
    -- Test get_salary_by_id function
    v_salary := emp_pkg.get_salary_by_id(v_employee_id);
    DBMS_OUTPUT.PUT_LINE('Employee ' || v_employee_id || ' salary: $' || TO_CHAR(v_salary, '999,999.99'));
    
    -- Test calculate_annual_bonus function
    v_bonus := emp_pkg.calculate_annual_bonus(v_employee_id, 15);
    DBMS_OUTPUT.PUT_LINE('Employee ' || v_employee_id || ' annual bonus (15%): $' || TO_CHAR(v_bonus, '999,999.99'));
    
    -- Test get_employee_count function
    DBMS_OUTPUT.PUT_LINE('Total employee count: ' || emp_pkg.get_employee_count);
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error testing functions: ' || SQLERRM);
END;
/

-- =====================================================
-- 5. RUN COMPREHENSIVE PL/SQL DEMONSTRATION
-- =====================================================

BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== RUNNING COMPREHENSIVE DEMONSTRATION ===');
    demo_plsql_features;
END;
/

-- =====================================================
-- 6. COMPLETION MESSAGE
-- =====================================================

BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('========================================');
    DBMS_OUTPUT.PUT_LINE('Sample Data Loading Complete!');
    DBMS_OUTPUT.PUT_LINE('========================================');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('The database is now ready for testing with:');
    DBMS_OUTPUT.PUT_LINE('- 10 sample employees');
    DBMS_OUTPUT.PUT_LINE('- Audit log entries from triggers');
    DBMS_OUTPUT.PUT_LINE('- All PL/SQL features demonstrated');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('You can now run the C# WinForms application!');
    DBMS_OUTPUT.PUT_LINE('========================================');
END;
/

-- Optional: Display final data verification
SELECT 'EMPLOYEES' as table_name, COUNT(*) as record_count FROM employees
UNION ALL
SELECT 'EMPLOYEE_LOG' as table_name, COUNT(*) as record_count FROM employee_log
ORDER BY table_name;

-- Show recent audit log entries
SELECT 
    log_id,
    employee_id,
    action,
    TO_CHAR(log_date, 'YYYY-MM-DD HH24:MI:SS') as log_date,
    old_salary,
    new_salary
FROM employee_log 
ORDER BY log_date DESC, log_id DESC;
