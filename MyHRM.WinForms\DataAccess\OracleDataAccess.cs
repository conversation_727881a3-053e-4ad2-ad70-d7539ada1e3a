using Oracle.ManagedDataAccess.Client;
using System.Configuration;
using System.Data;
using MyHRM.WinForms.Models;

namespace MyHRM.WinForms.DataAccess
{
    /// <summary>
    /// Oracle database access layer for HR Management System
    /// </summary>
    public class OracleDataAccess
    {
        private readonly string _connectionString;

        public OracleDataAccess()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["OracleConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("Oracle connection string not found in configuration.");
        }

        /// <summary>
        /// Test database connection
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();
                return connection.State == ConnectionState.Open;
            }
            catch (Exception ex)
            {
                throw new Exception($"Database connection failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Add employee using stored procedure
        /// </summary>
        public async Task<int> AddEmployeeAsync(string name, DateTime hireDate, decimal salary)
        {
            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("BEGIN emp_pkg.add_employee(:p_name, :p_hire_date, :p_salary, :p_employee_id); END;", connection)
                {
                    CommandType = CommandType.Text
                };

                // Input parameters
                command.Parameters.Add("p_name", OracleDbType.Varchar2, name, ParameterDirection.Input);
                command.Parameters.Add("p_hire_date", OracleDbType.Date, hireDate, ParameterDirection.Input);
                command.Parameters.Add("p_salary", OracleDbType.Decimal, salary, ParameterDirection.Input);

                // Output parameter for employee ID
                var outputParam = new OracleParameter("p_employee_id", OracleDbType.Int32)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                await command.ExecuteNonQueryAsync();

                // Return the generated employee ID
                return outputParam.Value != null && outputParam.Value != DBNull.Value
                    ? Convert.ToInt32(outputParam.Value)
                    : 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error adding employee: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get all employees
        /// </summary>
        public async Task<List<Employee>> GetAllEmployeesAsync()
        {
            var employees = new List<Employee>();

            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("SELECT id, name, hire_date, salary FROM employees ORDER BY id", connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    employees.Add(new Employee
                    {
                        Id = reader.GetInt32("id"),
                        Name = reader.GetString("name"),
                        HireDate = reader.GetDateTime("hire_date"),
                        Salary = reader.GetDecimal("salary")
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving employees: {ex.Message}", ex);
            }

            return employees;
        }

        /// <summary>
        /// Get employee salary by ID using function
        /// </summary>
        public async Task<decimal> GetEmployeeSalaryAsync(int employeeId)
        {
            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("SELECT emp_pkg.get_salary_by_id(:p_employee_id) FROM dual", connection);
                command.Parameters.Add("p_employee_id", OracleDbType.Int32, employeeId, ParameterDirection.Input);

                var result = await command.ExecuteScalarAsync();
                return result != null && result != DBNull.Value ? Convert.ToDecimal(result) : 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting employee salary: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get employee audit logs
        /// </summary>
        public async Task<List<EmployeeLog>> GetEmployeeLogsAsync()
        {
            var logs = new List<EmployeeLog>();

            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("SELECT log_id, employee_id, action, log_date FROM employee_log ORDER BY log_date DESC", connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    logs.Add(new EmployeeLog
                    {
                        LogId = reader.GetInt32("log_id"),
                        EmployeeId = reader.GetInt32("employee_id"),
                        Action = reader.GetString("action"),
                        LogDate = reader.GetDateTime("log_date")
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving employee logs: {ex.Message}", ex);
            }

            return logs;
        }
    }
}
