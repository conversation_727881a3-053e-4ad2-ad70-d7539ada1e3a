namespace MyHRM.WinForms
{
    partial class MainForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lblTitle = new Label();
            this.grpEmployeeInput = new GroupBox();
            this.lblName = new Label();
            this.txtName = new TextBox();
            this.lblHireDate = new Label();
            this.dtpHireDate = new DateTimePicker();
            this.lblSalary = new Label();
            this.txtSalary = new TextBox();
            this.btnAddEmployee = new Button();
            this.grpActions = new GroupBox();
            this.btnViewEmployees = new Button();
            this.btnTestConnection = new Button();
            this.btnTestPLSQL = new Button();
            this.dgvEmployees = new DataGridView();
            this.grpLogs = new GroupBox();
            this.dgvLogs = new DataGridView();
            this.btnViewLogs = new Button();
            this.lblStatus = new Label();
            this.grpEmployeeInput.SuspendLayout();
            this.grpActions.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvEmployees)).BeginInit();
            this.grpLogs.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvLogs)).BeginInit();
            this.SuspendLayout();
            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Microsoft Sans Serif", 16F, FontStyle.Bold, GraphicsUnit.Point);
            this.lblTitle.Location = new Point(12, 9);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(285, 26);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "HR Management System";
            // 
            // grpEmployeeInput
            // 
            this.grpEmployeeInput.Controls.Add(this.btnAddEmployee);
            this.grpEmployeeInput.Controls.Add(this.txtSalary);
            this.grpEmployeeInput.Controls.Add(this.lblSalary);
            this.grpEmployeeInput.Controls.Add(this.dtpHireDate);
            this.grpEmployeeInput.Controls.Add(this.lblHireDate);
            this.grpEmployeeInput.Controls.Add(this.txtName);
            this.grpEmployeeInput.Controls.Add(this.lblName);
            this.grpEmployeeInput.Location = new Point(12, 50);
            this.grpEmployeeInput.Name = "grpEmployeeInput";
            this.grpEmployeeInput.Size = new Size(400, 180);
            this.grpEmployeeInput.TabIndex = 1;
            this.grpEmployeeInput.TabStop = false;
            this.grpEmployeeInput.Text = "Add New Employee";
            // 
            // lblName
            // 
            this.lblName.AutoSize = true;
            this.lblName.Location = new Point(15, 30);
            this.lblName.Name = "lblName";
            this.lblName.Size = new Size(42, 15);
            this.lblName.TabIndex = 0;
            this.lblName.Text = "Name:";
            // 
            // txtName
            // 
            this.txtName.Location = new Point(15, 48);
            this.txtName.MaxLength = 100;
            this.txtName.Name = "txtName";
            this.txtName.Size = new Size(360, 23);
            this.txtName.TabIndex = 1;
            // 
            // lblHireDate
            // 
            this.lblHireDate.AutoSize = true;
            this.lblHireDate.Location = new Point(15, 80);
            this.lblHireDate.Name = "lblHireDate";
            this.lblHireDate.Size = new Size(61, 15);
            this.lblHireDate.TabIndex = 2;
            this.lblHireDate.Text = "Hire Date:";
            // 
            // dtpHireDate
            // 
            this.dtpHireDate.Format = DateTimePickerFormat.Short;
            this.dtpHireDate.Location = new Point(15, 98);
            this.dtpHireDate.Name = "dtpHireDate";
            this.dtpHireDate.Size = new Size(200, 23);
            this.dtpHireDate.TabIndex = 3;
            // 
            // lblSalary
            // 
            this.lblSalary.AutoSize = true;
            this.lblSalary.Location = new Point(230, 80);
            this.lblSalary.Name = "lblSalary";
            this.lblSalary.Size = new Size(41, 15);
            this.lblSalary.TabIndex = 4;
            this.lblSalary.Text = "Salary:";
            // 
            // txtSalary
            // 
            this.txtSalary.Location = new Point(230, 98);
            this.txtSalary.Name = "txtSalary";
            this.txtSalary.Size = new Size(145, 23);
            this.txtSalary.TabIndex = 5;
            // 
            // btnAddEmployee
            // 
            this.btnAddEmployee.BackColor = Color.LightGreen;
            this.btnAddEmployee.Font = new Font("Microsoft Sans Serif", 10F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnAddEmployee.Location = new Point(15, 135);
            this.btnAddEmployee.Name = "btnAddEmployee";
            this.btnAddEmployee.Size = new Size(120, 35);
            this.btnAddEmployee.TabIndex = 6;
            this.btnAddEmployee.Text = "Add Employee";
            this.btnAddEmployee.UseVisualStyleBackColor = false;
            this.btnAddEmployee.Click += new EventHandler(this.btnAddEmployee_Click);
            // 
            // grpActions
            // 
            this.grpActions.Controls.Add(this.btnTestPLSQL);
            this.grpActions.Controls.Add(this.btnTestConnection);
            this.grpActions.Controls.Add(this.btnViewEmployees);
            this.grpActions.Location = new Point(430, 50);
            this.grpActions.Name = "grpActions";
            this.grpActions.Size = new Size(350, 180);
            this.grpActions.TabIndex = 2;
            this.grpActions.TabStop = false;
            this.grpActions.Text = "Actions";
            // 
            // btnViewEmployees
            // 
            this.btnViewEmployees.BackColor = Color.LightBlue;
            this.btnViewEmployees.Font = new Font("Microsoft Sans Serif", 10F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnViewEmployees.Location = new Point(15, 30);
            this.btnViewEmployees.Name = "btnViewEmployees";
            this.btnViewEmployees.Size = new Size(130, 35);
            this.btnViewEmployees.TabIndex = 0;
            this.btnViewEmployees.Text = "View Employees";
            this.btnViewEmployees.UseVisualStyleBackColor = false;
            this.btnViewEmployees.Click += new EventHandler(this.btnViewEmployees_Click);
            // 
            // btnTestConnection
            // 
            this.btnTestConnection.BackColor = Color.LightYellow;
            this.btnTestConnection.Font = new Font("Microsoft Sans Serif", 10F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnTestConnection.Location = new Point(15, 80);
            this.btnTestConnection.Name = "btnTestConnection";
            this.btnTestConnection.Size = new Size(130, 35);
            this.btnTestConnection.TabIndex = 1;
            this.btnTestConnection.Text = "Test Connection";
            this.btnTestConnection.UseVisualStyleBackColor = false;
            this.btnTestConnection.Click += new EventHandler(this.btnTestConnection_Click);
            // 
            // btnTestPLSQL
            // 
            this.btnTestPLSQL.BackColor = Color.LightCoral;
            this.btnTestPLSQL.Font = new Font("Microsoft Sans Serif", 10F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnTestPLSQL.Location = new Point(15, 130);
            this.btnTestPLSQL.Name = "btnTestPLSQL";
            this.btnTestPLSQL.Size = new Size(130, 35);
            this.btnTestPLSQL.TabIndex = 2;
            this.btnTestPLSQL.Text = "Test PL/SQL";
            this.btnTestPLSQL.UseVisualStyleBackColor = false;
            this.btnTestPLSQL.Click += new EventHandler(this.btnTestPLSQL_Click);
            // 
            // dgvEmployees
            // 
            this.dgvEmployees.AllowUserToAddRows = false;
            this.dgvEmployees.AllowUserToDeleteRows = false;
            this.dgvEmployees.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvEmployees.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvEmployees.Location = new Point(12, 250);
            this.dgvEmployees.Name = "dgvEmployees";
            this.dgvEmployees.ReadOnly = true;
            this.dgvEmployees.RowTemplate.Height = 25;
            this.dgvEmployees.Size = new Size(768, 200);
            this.dgvEmployees.TabIndex = 3;
            // 
            // grpLogs
            // 
            this.grpLogs.Controls.Add(this.btnViewLogs);
            this.grpLogs.Controls.Add(this.dgvLogs);
            this.grpLogs.Location = new Point(12, 470);
            this.grpLogs.Name = "grpLogs";
            this.grpLogs.Size = new Size(768, 200);
            this.grpLogs.TabIndex = 4;
            this.grpLogs.TabStop = false;
            this.grpLogs.Text = "Employee Audit Logs";
            // 
            // dgvLogs
            // 
            this.dgvLogs.AllowUserToAddRows = false;
            this.dgvLogs.AllowUserToDeleteRows = false;
            this.dgvLogs.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvLogs.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvLogs.Location = new Point(15, 50);
            this.dgvLogs.Name = "dgvLogs";
            this.dgvLogs.ReadOnly = true;
            this.dgvLogs.RowTemplate.Height = 25;
            this.dgvLogs.Size = new Size(738, 135);
            this.dgvLogs.TabIndex = 0;
            // 
            // btnViewLogs
            // 
            this.btnViewLogs.BackColor = Color.LightGray;
            this.btnViewLogs.Font = new Font("Microsoft Sans Serif", 9F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnViewLogs.Location = new Point(15, 20);
            this.btnViewLogs.Name = "btnViewLogs";
            this.btnViewLogs.Size = new Size(100, 25);
            this.btnViewLogs.TabIndex = 1;
            this.btnViewLogs.Text = "View Logs";
            this.btnViewLogs.UseVisualStyleBackColor = false;
            this.btnViewLogs.Click += new EventHandler(this.btnViewLogs_Click);
            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.Font = new Font("Microsoft Sans Serif", 9F, FontStyle.Regular, GraphicsUnit.Point);
            this.lblStatus.Location = new Point(12, 685);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(41, 15);
            this.lblStatus.TabIndex = 5;
            this.lblStatus.Text = "Ready";
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 720);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.grpLogs);
            this.Controls.Add(this.dgvEmployees);
            this.Controls.Add(this.grpActions);
            this.Controls.Add(this.grpEmployeeInput);
            this.Controls.Add(this.lblTitle);
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Name = "MainForm";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "MyHRM - HR Management System";
            this.Load += new EventHandler(this.MainForm_Load);
            this.grpEmployeeInput.ResumeLayout(false);
            this.grpEmployeeInput.PerformLayout();
            this.grpActions.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvEmployees)).EndInit();
            this.grpLogs.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvLogs)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private Label lblTitle;
        private GroupBox grpEmployeeInput;
        private Label lblName;
        private TextBox txtName;
        private Label lblHireDate;
        private DateTimePicker dtpHireDate;
        private Label lblSalary;
        private TextBox txtSalary;
        private Button btnAddEmployee;
        private GroupBox grpActions;
        private Button btnViewEmployees;
        private Button btnTestConnection;
        private Button btnTestPLSQL;
        private DataGridView dgvEmployees;
        private GroupBox grpLogs;
        private DataGridView dgvLogs;
        private Button btnViewLogs;
        private Label lblStatus;
    }
}
